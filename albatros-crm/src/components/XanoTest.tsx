import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Alert, CircularProgress } from '@mui/material';
import { xanoService } from '../services/api/xanoService';
import { golfersAPI } from '../services/api/golfers';
import config from '../config';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
}

export const XanoTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (testName: string, status: TestResult['status'], message: string, data?: any) => {
    setTests(prev => {
      const existing = prev.find(t => t.test === testName);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.data = data;
        return [...prev];
      } else {
        return [...prev, { test: testName, status, message, data }];
      }
    });
  };

  const runTests = async () => {
    setIsRunning(true);
    setTests([]);

    // Test 1: Configuration
    updateTest('Configuration', 'pending', 'Checking configuration...');
    try {
      const configData = {
        useMockData: config.useMockData,
        xanoBaseUrl: config.xanoBaseUrl,
        courseId: config.courseId,
      };
      updateTest('Configuration', 'success', 'Configuration loaded successfully', configData);
    } catch (error: any) {
      updateTest('Configuration', 'error', `Configuration error: ${error.message}`);
    }

    // Test 2: XANO API Connection
    updateTest('XANO Connection', 'pending', 'Testing XANO API connection...');
    try {
      const response = await xanoService.getCourseInfo(config.courseId);
      updateTest('XANO Connection', 'success', 'XANO API connection successful', response.data);
    } catch (error: any) {
      updateTest('XANO Connection', 'error', `XANO connection failed: ${error.message}`);
    }

    // Test 3: Golfers API
    updateTest('Golfers API', 'pending', 'Testing golfers API...');
    try {
      const response = await golfersAPI.getGolfers(config.courseId);
      updateTest('Golfers API', 'success', `Retrieved ${response.data.length} golfers`, response.data.slice(0, 3));
    } catch (error: any) {
      updateTest('Golfers API', 'error', `Golfers API failed: ${error.message}`);
    }

    // Test 4: Dashboard API
    updateTest('Dashboard API', 'pending', 'Testing dashboard API...');
    try {
      const response = await xanoService.getDashboardOverview(config.courseId);
      updateTest('Dashboard API', 'success', 'Dashboard data retrieved successfully', response.data);
    } catch (error: any) {
      updateTest('Dashboard API', 'error', `Dashboard API failed: ${error.message}`);
    }

    setIsRunning(false);
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'pending': return 'info';
      default: return 'info';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⏳';
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Typography variant="h4" gutterBottom>
        XANO Integration Test
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        This component tests the XANO backend integration to ensure all API calls are working correctly.
      </Typography>

      <Button 
        variant="contained" 
        onClick={runTests} 
        disabled={isRunning}
        sx={{ mb: 3 }}
      >
        {isRunning ? <CircularProgress size={20} sx={{ mr: 1 }} /> : null}
        {isRunning ? 'Running Tests...' : 'Run Tests'}
      </Button>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {tests.map((test, index) => (
          <Alert 
            key={index}
            severity={getStatusColor(test.status)}
            sx={{ 
              '& .MuiAlert-message': { 
                width: '100%' 
              }
            }}
          >
            <Box>
              <Typography variant="h6">
                {getStatusIcon(test.status)} {test.test}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                {test.message}
              </Typography>
              {test.data && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                    Data:
                  </Typography>
                  <pre style={{ 
                    fontSize: '12px', 
                    background: 'rgba(0,0,0,0.1)', 
                    padding: '8px', 
                    borderRadius: '4px',
                    overflow: 'auto',
                    maxHeight: '200px'
                  }}>
                    {JSON.stringify(test.data, null, 2)}
                  </pre>
                </Box>
              )}
            </Box>
          </Alert>
        ))}
      </Box>

      {tests.length === 0 && !isRunning && (
        <Alert severity="info">
          Click "Run Tests" to test the XANO integration.
        </Alert>
      )}
    </Box>
  );
};
