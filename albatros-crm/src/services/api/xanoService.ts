import { xanoAPI } from '../xanoAPI';
import config from '../../config';
import { ApiResponse } from './types';
import {
  Golfer,
  TeeTime,
  TeeTimeBooking,
  CalendarEvent,
  ProShopItem,
  FoodAndBeverageItem,
  Transaction,
  GolfCourse,
  User,
  QueryParams,
} from '../../types/xano';

// Service layer that adapts XANO API to existing application interfaces
export class XanoService {
  // Dashboard/Analytics methods
  async getDashboardOverview(courseId: string, timeframe: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<ApiResponse<any>> {
    try {
      // Get various data points for dashboard
      const [transactions, teeTimes, golfers] = await Promise.all([
        xanoAPI.transaction.getAll({ golf_course_id: parseInt(courseId) }),
        xanoAPI.teeTime.getAll({ golf_course_id: parseInt(courseId) }),
        xanoAPI.golfer.getAll({ golf_course_id: parseInt(courseId) }),
      ]);

      // Calculate metrics
      const totalRevenue = transactions.data.reduce((sum: number, t: Transaction) => sum + t.transaction_amount, 0);
      const totalBookings = teeTimes.data.length;
      const averageRating = 4.5; // This would come from reviews
      
      return {
        data: {
          totalRevenue,
          totalBookings,
          averageRating,
          weather: 'Sunny', // This would come from weather API
        },
        status: 200,
        message: 'OK',
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Internal Server Error',
      };
    }
  }

  // Golfer management methods
  async getGolfers(courseId: string): Promise<ApiResponse<Golfer[]>> {
    try {
      const response = await xanoAPI.golfer.getAll({ 
        golf_course_id: parseInt(courseId) 
      });
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: [],
        status: error.status || 500,
        message: error.message || 'Failed to fetch golfers',
      };
    }
  }

  async addGolfer(courseId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> {
    try {
      const golferData = {
        ...golfer,
        golf_course_id: parseInt(courseId),
      };
      
      const response = await xanoAPI.golfer.create(golferData);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Failed to add golfer',
      };
    }
  }

  async updateGolfer(courseId: string, golferId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> {
    try {
      const response = await xanoAPI.golfer.update(golferId, golfer);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Failed to update golfer',
      };
    }
  }

  // Tee Time management methods
  async getTeeTimes(courseId: string, date?: string): Promise<ApiResponse<TeeTime[]>> {
    try {
      const params: QueryParams = { 
        golf_course_id: parseInt(courseId) 
      };
      
      if (date) {
        // Add date filtering if needed
        params.filter = { tee_time: date };
      }
      
      const response = await xanoAPI.teeTime.getAll(params);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: [],
        status: error.status || 500,
        message: error.message || 'Failed to fetch tee times',
      };
    }
  }

  async bookTeeTime(courseId: string, booking: Partial<TeeTimeBooking>): Promise<ApiResponse<TeeTimeBooking>> {
    try {
      const bookingData = {
        ...booking,
        golf_course_id: parseInt(courseId),
        booking_status: 'confirmed',
      };
      
      const response = await xanoAPI.teeTimeBooking.create(bookingData);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Failed to book tee time',
      };
    }
  }

  // Pro Shop methods
  async getProShopItems(courseId: string): Promise<ApiResponse<ProShopItem[]>> {
    try {
      const response = await xanoAPI.proShopItem.getAll({ 
        golf_course_id: parseInt(courseId) 
      });
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: [],
        status: error.status || 500,
        message: error.message || 'Failed to fetch pro shop items',
      };
    }
  }

  // Food & Beverage methods
  async getFoodBeverageItems(courseId: string): Promise<ApiResponse<FoodAndBeverageItem[]>> {
    try {
      const response = await xanoAPI.foodBeverageItem.getAll({ 
        golf_course_id: parseInt(courseId) 
      });
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: [],
        status: error.status || 500,
        message: error.message || 'Failed to fetch food & beverage items',
      };
    }
  }

  // Transaction methods
  async getTransactions(courseId: string): Promise<ApiResponse<Transaction[]>> {
    try {
      const response = await xanoAPI.transaction.getAll({ 
        golf_course_id: parseInt(courseId) 
      });
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: [],
        status: error.status || 500,
        message: error.message || 'Failed to fetch transactions',
      };
    }
  }

  async createTransaction(courseId: string, transaction: Partial<Transaction>): Promise<ApiResponse<Transaction>> {
    try {
      const transactionData = {
        ...transaction,
        golf_course_id: parseInt(courseId),
        transaction_datetime: new Date().toISOString(),
        transaction_status: 'completed',
      };
      
      const response = await xanoAPI.transaction.create(transactionData);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Failed to create transaction',
      };
    }
  }

  // Course information methods
  async getCourseInfo(courseId: string): Promise<ApiResponse<GolfCourse>> {
    try {
      const response = await xanoAPI.golfCourse.getById(courseId);
      
      return {
        data: response.data,
        status: response.status,
        message: response.statusText,
      };
    } catch (error: any) {
      throw {
        data: null,
        status: error.status || 500,
        message: error.message || 'Failed to fetch course info',
      };
    }
  }

  // Weather method (placeholder - would integrate with weather API)
  async getWeather(): Promise<ApiResponse<any>> {
    return {
      data: {
        temperature: 72,
        conditions: 'Sunny',
        windSpeed: 5,
        precipitation: 0,
      },
      status: 200,
      message: 'OK',
    };
  }

  // Authentication methods
  async login(email: string, password: string) {
    return xanoAPI.auth.login({ email, password });
  }

  async signup(email: string, password: string, name?: string) {
    return xanoAPI.auth.signup({ email, password, name });
  }

  async getCurrentUser() {
    return xanoAPI.auth.me();
  }

  logout() {
    xanoAPI.auth.logout();
  }
}

// Create and export singleton instance
export const xanoService = new XanoService();
export default xanoService;
