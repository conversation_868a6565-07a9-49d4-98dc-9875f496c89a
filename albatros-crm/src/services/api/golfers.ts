import { api, ApiResponse, useMockData } from './config';
import { Golfer } from './types';
import { mockAPI } from './mockData';
import { xanoService } from './xanoService';
import {
  adaptXanoGolferToApi,
  adaptApiGolferToXano,
  adaptArrayToApi,
  adaptXanoResponseToApi
} from './adapters';

export const golfersAPI = {
  getGolfers: async (courseId: string): Promise<ApiResponse<Golfer[]>> => {
    if (useMockData) {
      return mockAPI.getGolfers(courseId);
    }
    const xanoResponse = await xanoService.getGolfers(courseId);
    return adaptXanoResponseToApi(xanoResponse, (data) =>
      adaptArrayToApi(data, adaptXanoGolferToApi)
    );
  },

  addGolfer: async (courseId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    if (useMockData) {
      return mockAPI.addGolfer(courseId, golfer);
    }
    const xanoGolferData = adaptApiGol<PERSON><PERSON><PERSON><PERSON><PERSON>(golfer);
    const xanoResponse = await xanoService.addGolfer(courseId, xanoGolferData);
    return adaptXanoResponseToApi(xanoResponse, adaptXanoGolferToApi);
  },

  updateGolfer: async (courseId: string, golferId: string, golfer: Partial<Golfer>): Promise<ApiResponse<Golfer>> => {
    if (useMockData) {
      return mockAPI.updateGolfer(courseId, golferId, golfer);
    }
    const xanoGolferData = adaptApiGolferToXano(golfer);
    const xanoResponse = await xanoService.updateGolfer(courseId, golferId, xanoGolferData);
    return adaptXanoResponseToApi(xanoResponse, adaptXanoGolferToApi);
  }
};