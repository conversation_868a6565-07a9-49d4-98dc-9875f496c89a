// Type adapters to convert between XANO types and existing API types
import { Golfer as XanoGolfer, TeeTime as XanoTeeTime, Transaction as XanoTransaction } from '../../types/xano';
import { Golfer as ApiGolfer, TeeTime as ApiTeeTime, ApiResponse } from './types';

// Convert XANO Golfer to API Golfer format
export function adaptXanoGolferToApi(xanoGolfer: XanoGolfer): ApiGolfer {
  return {
    id: xanoGolfer.id.toString(),
    courseId: '1', // Default course ID for compatibility
    name: `${xanoGolfer.first_name} ${xanoGolfer.last_name}`,
    email: xanoGolfer.email,
    phone: xanoGolfer.phone_number || '',
    handicap: xanoGolfer.handicap || 0,
    membershipType: 'regular', // Default membership type
    status: 'active', // Default status
  };
}

// Convert API Golfer to XANO Golfer format
export function adaptApiGolferToXano(apiGolfer: Partial<ApiGolfer>): Partial<XanoGolfer> {
  const nameParts = apiGolfer.name?.split(' ') || ['', ''];
  return {
    first_name: nameParts[0] || '',
    last_name: nameParts.slice(1).join(' ') || '',
    email: apiGolfer.email || '',
    phone_number: apiGolfer.phone,
    handicap: apiGolfer.handicap,
  };
}

// Convert XANO TeeTime to API TeeTime format
export function adaptXanoTeeTimeToApi(xanoTeeTime: XanoTeeTime): ApiTeeTime {
  return {
    id: xanoTeeTime.id.toString(),
    courseId: xanoTeeTime.golf_course_id.toString(),
    date: xanoTeeTime.tee_time.split('T')[0], // Extract date part
    time: xanoTeeTime.tee_time.split('T')[1]?.split('.')[0] || '', // Extract time part
    players: [], // Would need to fetch related golfer data
    status: 'booked',
    price: xanoTeeTime.tee_time_cost_per_golfer,
    notes: xanoTeeTime.notes || '',
  };
}

// Convert API TeeTime to XANO TeeTime format
export function adaptApiTeeTimeToXano(apiTeeTime: Partial<ApiTeeTime>): Partial<XanoTeeTime> {
  const dateTime = `${apiTeeTime.date}T${apiTeeTime.time}`;
  return {
    tee_time: dateTime,
    number_of_players: apiTeeTime.players?.length || 1,
    tee_time_cost_per_golfer: apiTeeTime.price || 0,
    notes: apiTeeTime.notes,
  };
}

// Convert XANO Transaction to API format (if needed)
export function adaptXanoTransactionToApi(xanoTransaction: XanoTransaction) {
  return {
    id: xanoTransaction.id.toString(),
    amount: xanoTransaction.transaction_amount,
    date: xanoTransaction.transaction_datetime,
    type: xanoTransaction.revenue_type || 'unknown',
    status: xanoTransaction.transaction_status,
    golferId: xanoTransaction.golfer_id.toString(),
  };
}

// Generic adapter for arrays
export function adaptArrayToApi<T, U>(
  xanoArray: T[],
  adapter: (item: T) => U
): U[] {
  return xanoArray.map(adapter);
}

// Generic adapter for API responses
export function adaptXanoResponseToApi<T, U>(
  xanoResponse: { data: T; status: number; statusText: string },
  adapter: (data: T) => U
): ApiResponse<U> {
  return {
    data: adapter(xanoResponse.data),
    status: xanoResponse.status,
    message: xanoResponse.statusText,
  };
}
