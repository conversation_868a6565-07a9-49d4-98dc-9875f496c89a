import { xanoService } from './api/xanoService';
import config from '../config';

interface User {
  id: number;
  name: string;
  email: string;
  created_at: string;
}

interface LoginResponse {
  token: string;
  user: User;
}

interface SignupResponse {
  token: string;
  user: User;
}

class AuthService {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('authToken');
  }

  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await xanoService.login(email, password);
      const { authToken, user } = response.data;

      this.token = authToken;
      localStorage.setItem('authToken', authToken);

      return { token: authToken, user };
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    }
  }

  async signup(name: string, email: string, password: string): Promise<SignupResponse> {
    try {
      const response = await xanoService.signup(email, password, name);
      const { authToken, user } = response.data;

      this.token = authToken;
      localStorage.setItem('authToken', authToken);

      return { token: authToken, user };
    } catch (error: any) {
      console.error('Signup error:', error);
      throw new Error(error.message || 'Signup failed');
    }
  }

  async me(): Promise<User> {
    if (!this.token) {
      throw new Error('No authentication token');
    }

    try {
      const response = await xanoService.getCurrentUser();
      return response.data;
    } catch (error: any) {
      console.error('Get user error:', error);
      throw new Error(error.message || 'Failed to get user info');
    }
  }

  logout(): void {
    this.token = null;
    localStorage.removeItem('authToken');
    xanoService.logout();
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }
}

export const auth = new AuthService(); 