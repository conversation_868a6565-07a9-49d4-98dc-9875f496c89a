import axios, { AxiosInstance, AxiosResponse } from 'axios';
import config from '../config';
import {
  XanoApiResponse,
  XanoError,
  QueryParams,
  AuthResponse,
  LoginRequest,
  SignupRequest,
  GolfCourse,
  Golfer,
  TeeTime,
  TeeTimeBooking,
  CalendarEvent,
  ProShopItem,
  ProShopItemTransaction,
  FoodAndBeverageItem,
  FoodAndBeverageItemTransaction,
  Transaction,
  AccountingAccount,
  AccountingPeriod,
  AccountingTransaction,
  User,
  Membership,
} from '../types/xano';

class XanoAPIClient {
  private api: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: config.xanoBaseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor for auth token
    this.api.interceptors.request.use((config) => {
      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('XANO API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );

    // Load token from localStorage if available
    const storedToken = localStorage.getItem('xano_token');
    if (storedToken) {
      this.setAuthToken(storedToken);
    }
  }

  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('xano_token', token);
  }

  clearAuthToken() {
    this.authToken = null;
    localStorage.removeItem('xano_token');
  }

  // Generic request method
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    endpoint: string,
    data?: any,
    params?: any
  ): Promise<XanoApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.api.request({
        method,
        url: endpoint,
        data,
        params,
      });

      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
      };
    } catch (error: any) {
      throw {
        code: error.response?.data?.code || 'API_ERROR',
        message: error.response?.data?.message || error.message || 'An error occurred',
        details: error.response?.data,
      } as XanoError;
    }
  }

  // Authentication endpoints
  auth = {
    login: async (credentials: LoginRequest) => {
      const response = await this.request<AuthResponse>('POST', '/auth/login', credentials);

      if (response.data.authToken) {
        this.setAuthToken(response.data.authToken);
      }

      return response;
    },

    signup: async (userData: SignupRequest) => {
      return this.request<AuthResponse>('POST', '/auth/signup', userData);
    },

    me: async () => {
      return this.request<User>('GET', '/auth/me');
    },

    logout: () => {
      this.clearAuthToken();
    },
  };

  // Golf Course endpoints
  golfCourse = {
    getAll: async (params?: QueryParams) => {
      return this.request<GolfCourse[]>('GET', '/golf_course', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<GolfCourse>('GET', `/golf_course/${id}`);
    },

    create: async (data: Partial<GolfCourse>) => {
      return this.request<GolfCourse>('POST', '/golf_course', data);
    },

    update: async (id: string, data: Partial<GolfCourse>) => {
      return this.request<GolfCourse>('PATCH', `/golf_course/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<void>('DELETE', `/golf_course/${id}`);
    },
  };

  // Golfer endpoints
  golfer = {
    getAll: async (params?: QueryParams) => {
      return this.request<Golfer[]>('GET', '/golfer', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<Golfer>('GET', `/golfer/${id}`);
    },

    create: async (data: Partial<Golfer>) => {
      return this.request<Golfer>('POST', '/golfer', data);
    },

    update: async (id: string, data: Partial<Golfer>) => {
      return this.request<Golfer>('PATCH', `/golfer/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<void>('DELETE', `/golfer/${id}`);
    },
  };

  // Tee Time endpoints
  teeTime = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/tee_time', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/tee_time/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/tee_time', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/tee_time/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/tee_time/${id}`);
    },
  };

  // Tee Time Booking endpoints
  teeTimeBooking = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/tee_time_booking', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/tee_time_booking/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/tee_time_booking', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PUT', `/tee_time_booking/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/tee_time_booking/${id}`);
    },
  };

  // Calendar Event endpoints
  calendarEvent = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/calendar_event', undefined, params);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/calendar_event', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PUT', `/calendar_event/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/calendar_event/${id}`);
    },
  };

  // Pro Shop Item endpoints
  proShopItem = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/pro_shop_item', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/pro_shop_item/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/pro_shop_item', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/pro_shop_item/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/pro_shop_item/${id}`);
    },
  };

  // Pro Shop Transaction endpoints
  proShopTransaction = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/pro_shop_item_transaction', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/pro_shop_item_transaction/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/pro_shop_item_transaction', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/pro_shop_item_transaction/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/pro_shop_item_transaction/${id}`);
    },
  };

  // Food & Beverage Item endpoints
  foodBeverageItem = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/food_and_beverage_item', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/food_and_beverage_item/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/food_and_beverage_item', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/food_and_beverage_item/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/food_and_beverage_item/${id}`);
    },
  };

  // Food & Beverage Transaction endpoints
  foodBeverageTransaction = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/food_and_beverage_item_transaction', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/food_and_beverage_item_transaction/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/food_and_beverage_item_transaction', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/food_and_beverage_item_transaction/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/food_and_beverage_item_transaction/${id}`);
    },
  };

  // Transaction endpoints
  transaction = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/transaction', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/transaction/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/transaction', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/transaction/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/transaction/${id}`);
    },
  };

  // Accounting Account endpoints
  accountingAccount = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/accounting_account', undefined, params);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/accounting_account', data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/accounting_account/${id}`);
    },
  };

  // Accounting Period endpoints
  accountingPeriod = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/accounting_period', undefined, params);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/accounting_period', data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/accounting_period/${id}`);
    },
  };

  // Accounting Transaction endpoints
  accountingTransaction = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/accounting_transaction', undefined, params);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/accounting_transaction', data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/accounting_transaction/${id}`);
    },
  };

  // User endpoints
  user = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/user', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/user/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/user', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PUT', `/user/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/user/${id}`);
    },
  };

  // Membership endpoints
  membership = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/membership', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/membership/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/membership', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PATCH', `/membership/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/membership/${id}`);
    },
  };

  // Analytics Report endpoints
  analyticsReport = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/analytics_report', undefined, params);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/analytics_report', data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/analytics_report/${id}`);
    },
  };

  // Marketing Campaign endpoints
  marketingCampaign = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/marketing_campaign', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/marketing_campaign/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/marketing_campaign', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PUT', `/marketing_campaign/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/marketing_campaign/${id}`);
    },
  };

  // Tournament Event endpoints
  tournamentEvent = {
    getAll: async (params?: any) => {
      return this.request<any[]>('GET', '/tournament_event', undefined, params);
    },

    getById: async (id: string) => {
      return this.request<any>('GET', `/tournament_event/${id}`);
    },

    create: async (data: any) => {
      return this.request<any>('POST', '/tournament_event', data);
    },

    update: async (id: string, data: any) => {
      return this.request<any>('PUT', `/tournament_event/${id}`, data);
    },

    delete: async (id: string) => {
      return this.request<any>('DELETE', `/tournament_event/${id}`);
    },
  };
}

// Create and export a singleton instance
export const xanoAPI = new XanoAPIClient();
export default xanoAPI;
