// XANO Database Schema Types
// Generated from golf_course_data_model.md

// Base interface for all entities
export interface BaseEntity {
  id: number;
  created_at: string;
}

// Golf Course Management
export interface GolfCourse extends BaseEntity {
  course_id: string;
  name: string;
  address: string;
  phone_number: string;
  email_address: string;
  course_logo?: string;
  has_gps: boolean;
  measurement: string;
  golf_course_hole_id?: number;
  course_settings?: any;
  seasonal_rate?: any;
  course_holidays?: any;
  course_hours?: any;
  pace_of_play_data_id?: number;
  golf_course_data_api?: any;
  google_average_rating?: number;
  google_total_reviews?: number;
  crm_average_course_quality?: number;
  crm_average_food_quality?: number;
  crm_average_overall_satisfaction?: number;
  crm_total_reviews?: number;
  time_tracking_settings?: any;
}

export interface GolfCourseHole extends BaseEntity {
  golf_course_id: number;
  hole_number: number;
  hole_par_men: number;
  hole_par_women: number;
  blue_tees_distance: number;
  gold_tees_distance: number;
  white_tees_distance: number;
  green_tees_distance: number;
  red_tees_distance: number;
  coordinates?: string;
  golf_course_hole_data?: any;
}

export interface GolfCourseAnnouncement extends BaseEntity {
  golf_course_id: number;
  announcement_title: string;
  announcement_content: string;
  start_datetime: string;
  end_datetime: string;
  global_data_id?: number;
  ai_agent_interaction_id?: number;
}

// User Management
export interface User extends BaseEntity {
  golf_course_id: number;
  name: string;
  email: string;
  password: string;
  corp_permission?: boolean;
  region_permission?: boolean;
  state_permission?: boolean;
  metro_permission?: boolean;
  course_permission?: boolean;
  calendar_access?: boolean;
  roster_access?: boolean;
  pro_shop_access?: boolean;
  nineteenth_hole_access?: boolean;
  tournaments_access?: boolean;
  analytics_access?: boolean;
  back_office_access?: boolean;
  settings_access?: boolean;
  user_role?: string;
  avatar?: string;
  last_login?: string;
  status: string;
}

export interface UserActivityLog extends BaseEntity {
  user_id: number;
  action_timestamp: string;
  module: string;
  action: string;
  details?: string;
  golf_course_id: number;
  global_data_id?: number;
}

// Customer Management
export interface Golfer extends BaseEntity {
  global_id?: string;
  crm_golfer_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  upcoming_tee_time?: any;
  past_tee_times?: any;
  past_food_orders?: any;
  backfill_presented?: boolean;
  backfill_accepted?: boolean;
  waitlist_enabled?: boolean;
  waitlist_dates_times?: any;
  preferred_play_day_times?: any;
  preferred_course?: string;
  round_history?: any;
  saved_payment_methods?: any;
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
  ai_agent_interaction_id?: number;
  payment_info_verified?: boolean;
  campaign_progress?: any;
  golfer_shipping_address_id?: number;
  handicap?: number;
}

export interface GolferGroup extends BaseEntity {
  group_name: string;
  golfer_ids: number[];
  golfer_preferences?: any;
  golf_course_id: number;
  send_group_email?: boolean;
  send_group_sms?: boolean;
  send_group_in_app?: boolean;
}

export interface GolferShippingAddress extends BaseEntity {
  golfer_id: number;
  street_address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

export interface GolferFunds extends BaseEntity {
  golfer_id: number;
  funds_balance: number;
  last_updated_at: string;
}

export interface CrmCourseReview extends BaseEntity {
  golf_course_id: number;
  golfer_id: number;
  review_date: string;
  review_text: string;
  course_quality_rating: number;
  food_quality_rating: number;
  overall_satisfaction_rating: number;
}

// Membership Management
export interface Membership extends BaseEntity {
  golf_course_id: number;
  membership_type: string;
  start_date: string;
  expiry_date: string;
  discount: number;
  golfer_id: number;
  membership_accepted_from_crm?: boolean;
}

// Tee Time Management
export interface CalendarEvent extends BaseEntity {
  golf_course_id: number;
  title: string;
  description?: string;
  start_datetime: string;
  end_datetime: string;
  location?: string;
  event_type: string;
  tee_time_interval?: number;
  availability_outlook?: string;
  cost_per_round?: number;
  discount_or_promotion_value?: number;
  max_golfers?: number;
  is_bookable?: boolean;
  global_data_id?: number;
  ai_agent_interaction_id?: number;
  membership_discount_id?: number;
  send_email_reminder?: boolean;
  send_sms_reminder?: boolean;
  send_in_app_reminder?: boolean;
}

export interface TeeTime extends BaseEntity {
  tee_time: string;
  number_of_players: number;
  golfer_id: number;
  golf_course_id: number;
  tee_time_cost_per_golfer: number;
  split_tee_time_cost_with_other_golfer?: boolean;
  tee_time_booking_id?: number;
  special_rate?: number;
  notes?: string;
}

export interface TeeTimeBooking extends BaseEntity {
  calendar_event_id: number;
  golfer_id: number;
  booking_status: string;
  global_data_id?: number;
}

export interface CourseWaitlistSetting extends BaseEntity {
  golf_course_id: number;
  waitlist_enabled: boolean;
  max_waitlist_size?: number;
  waitlist_join_message?: string;
  tee_time_available_message?: string;
  confirmation_timeframe?: number;
  ai_agent_interaction_id?: number;
}

// Pro Shop Management
export interface ProShopItem extends BaseEntity {
  golf_course_id: number;
  pro_shop_category_name: string;
  pro_shop_item_name: string;
  pro_shop_item_sku: string;
  description?: string;
  price: number;
  cost?: number;
  membership_discount_offered?: boolean;
  membership_discount_amount?: number;
  pro_shop_item_photo?: string;
  pro_shop_mobile_item_offered?: boolean;
  inventory_in_stock: number;
  total_inventory_necessary?: number;
  ai_agent_interaction_id?: number;
  membership_discount_id?: number;
  is_rentable?: boolean;
  rental_price?: number;
}

export interface ProShopItemTransaction extends BaseEntity {
  quantity: number;
  pro_shop_item_id: number;
  transaction_id: number;
  ai_agent_interaction_id?: number;
  transaction_datetime: string;
  golfer_id: number;
}

// Food & Beverage Management
export interface FoodAndBeverageItem extends BaseEntity {
  golf_course_id: number;
  food_and_beverage_category: string;
  food_item_name: string;
  description?: string;
  price: number;
  food_customization?: any;
  cost?: number;
  membership_offered_discount?: boolean;
  membership_discount_amount?: number;
  mobile_app_offered_food_item?: boolean;
  membership_discount_id?: number;
}

export interface FoodAndBeverageItemTransaction extends BaseEntity {
  quantity: number;
  food_and_beverage_item_id: number;
  transaction_id: number;
  order_status: string;
  golfer_id: number;
  claimed_by_employee_id?: number;
  delivery_time?: string;
  delivered_by_employee_id?: number;
  ai_agent_interaction_id?: number;
  in_play_mode_id?: number;
}

// Financial Management
export interface Transaction extends BaseEntity {
  transaction_amount: number;
  payment_method: string;
  golfer_id: number;
  payment_info_verified?: boolean;
  transaction_datetime: string;
  transaction_status: string;
  authorization_code?: string;
  e_commerce_transaction_id?: number;
  pro_shop_item_transaction_id?: number;
  food_and_beverage_item_transaction_id?: number;
  rewards_program_id?: number;
  square_transaction_id?: string;
  square_location_id?: string;
  transaction_uuid?: string;
  sensitive_payment_data_id?: number;
  revenue_type?: string;
  revenue_source?: string;
}

// Accounting
export interface AccountingAccount extends BaseEntity {
  account_name: string;
  account_type: string;
  account_number: string;
  golf_course_id: number;
  beginning_balance: number;
  ending_balance: number;
  is_reconciled?: boolean;
  reconciliation_date?: string;
  ai_agent_interaction_id?: number;
}

export interface AccountingPeriod extends BaseEntity {
  period_start_date: string;
  period_end_date: string;
  period_name: string;
  golf_course_id: number;
  ai_agent_interaction_id?: number;
}

export interface AccountingTransaction extends BaseEntity {
  transaction_date: string;
  description: string;
  amount: number;
  transaction_type: string;
  account_id: number;
  golf_course_id: number;
  golfer_id?: number;
  e_commerce_transaction_item_id?: number;
  food_and_beverage_item_transaction_id?: number;
  ai_agent_interaction_id?: number;
}

// API Response Types
export interface XanoApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

export interface XanoError {
  code: string;
  message: string;
  details?: any;
}

// Common query parameters
export interface QueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  filter?: Record<string, any>;
  golf_course_id?: number;
}

// Authentication types
export interface AuthResponse {
  authToken: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  name?: string;
}
