import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography,
  CircularProgress,
  CssBaseline,
  Theme,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  CalendarToday as CalendarIcon,
  Person as RosterIcon,
  ShoppingBag as ProShopIcon,
  LocalBar as NineteenthHoleIcon,
  EmojiEvents as EventsIcon,
  BarChart as AnalyticsIcon,
  Business as BackOfficeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import pages
import DashboardPage from './pages/DashboardPage';
import CalendarPage from './pages/CalendarPage';
import SetupPage from './pages/SetupPage';
import RosterPage from './pages/RosterPage';
import ProShopPage from './pages/ProShopPage';
import NineteenthHolePage from './pages/NineteenthHolePage';
import TeeSheetPage from './pages/TeeSheetPage';
import RestaurantPOSPage from './pages/RestaurantPOSPage';
import POSCheckoutPage from './pages/POSCheckoutPage';
import AnalyticsPage from './pages/Analytics';
// import BackOfficePage from './pages/BackOffice';
import SettingsPage from './pages/SettingsPage';
import ClubhousePage from './pages/ClubhousePage';

// Import components
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import BackOfficeModule from './components/BackOfficeModule';
import { CourseConfigProvider } from './components/Setup/CourseConfigContext';
import { AuthProvider } from './contexts/AuthContext';
import { XanoTest } from './components/XanoTest';

// Import services and config
import { courseAPI } from './services/api';
import config from './config';

// Import types
import { MenuItem } from './types/navigation';
import { CourseInfo } from './components/Sidebar/types';
import { DashboardPageProps } from './pages/types';
import ClubhouseIcon from './components/Sidebar/ClubhouseIcon';


const menuItems: MenuItem[] = [
  { 
    id: 'dashboard',
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: 'dashboard',
    label: 'Dashboard'
  },
  { 
    id: 'calendar',
    text: 'Calendar',
    icon: <CalendarIcon />,
    path: 'calendar',
    label: 'Calendar'
  },
  { 
    id: 'roster',
    text: 'Roster',
    icon: <RosterIcon />,
    path: 'roster',
    label: 'Roster'
  },
  { 
    id: 'pro-shop',
    text: 'Pro Shop',
    icon: <ProShopIcon />,
    path: 'pro-shop',
    label: 'Pro Shop'
  },
  { 
    id: '19th-hole',
    text: '19th Hole',
    icon: <NineteenthHoleIcon />,
    path: '19th-hole',
    label: '19th Hole'
  },
  { 
    id: 'events',
    text: 'Events & Tournaments',
    icon: <EventsIcon />,
    path: 'events',
    label: 'Events & Tournaments'
  },
  { 
    id: 'clubhouse',
    text: 'Clubhouse',
    icon: <ClubhouseIcon />,
    path: 'clubhouse',
    label: 'Clubhouse'
  },
  { 
    id: 'analytics',
    text: 'Analytics',
    icon: <AnalyticsIcon />,
    path: 'analytics',
    label: 'Analytics'
  },
  { 
    id: 'back-office',
    text: 'Back Office',
    icon: <BackOfficeIcon />,
    path: 'back-office',
    label: 'Back Office'
  },
  { 
    id: 'settings',
    text: 'Settings',
    icon: <SettingsIcon />,
    path: 'settings',
    label: 'Settings'
  }
];

/**
 * Main application component that handles the layout and routing
 */
const App: React.FC = () => {
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [courseInfo, setCourseInfo] = useState<CourseInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isDrawerExpanded, setIsDrawerExpanded] = useState<boolean>(false);
  const [timeView, setTimeView] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  useEffect(() => {
    const fetchCourseInfo = async (): Promise<void> => {
      try {
        setLoading(true);
        const response = await courseAPI.getCourseInfo(config.courseId);
        setCourseInfo(response.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCourseInfo();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">Error: {error}</Typography>
      </Box>
    );
  }

  const dashboardProps: DashboardPageProps = {
    courseId: config.courseId,
    timeView,
    setTimeView,
  };

  return (
    <AuthProvider>
      <CourseConfigProvider>
        <Router>
          <Box sx={{ display: 'flex' }}>
            <CssBaseline />
            <Sidebar
              isExpanded={isDrawerExpanded}
              onMouseEnter={() => setIsDrawerExpanded(true)}
              onMouseLeave={() => setIsDrawerExpanded(false)}
              selectedIndex={selectedIndex}
              onMenuSelect={setSelectedIndex}
              menuItems={menuItems}
              courseInfo={courseInfo}
            />
            <Box
              component="main"
              sx={{
                flexGrow: 1,
                p: 3,
                width: { sm: `6px` },
                marginLeft: { sm: `6px` },
                transition: isDrawerExpanded ? (theme: Theme) => theme.transitions.create(['margin-left', 'width'], {
                  easing: theme.transitions.easing.easeOut,
                  duration: 150,
                }) : 'none',
              }}
            >
              <Header courseInfo={courseInfo} />
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<DashboardPage {...dashboardProps} />} />
                <Route path="/calendar" element={<CalendarPage courseId="1" />} />
                <Route path="/tee-sheet" element={<TeeSheetPage cart={[]} setCart={() => {}} cartModalOpen={false} setCartModalOpen={() => {}} handleRemoveFromCart={() => {}} />} />
                <Route path="/roster" element={<RosterPage courseId="1" />} />
                <Route path="/pro-shop" element={<ProShopPage courseId={config.courseId} />} />
                <Route path="/pro-shop-pos" element={<POSCheckoutPage courseId={config.courseId} />} />
                <Route path="/19th-hole" element={<NineteenthHolePage courseId={config.courseId} />} />
                <Route path="/19th-hole-pos" element={<RestaurantPOSPage />} />
                <Route path="/pos-checkout" element={<POSCheckoutPage courseId={config.courseId} />} />
                <Route path="/settings" element={<SettingsPage courseId={config.courseId} />} />
                <Route path="/setup" element={<SetupPage />} />
                <Route path="/blank" element={<div>Blank Page (Coming Soon)</div>} />
                <Route path="/back-office" element={<BackOfficeModule />} />
                <Route path="/analytics" element={<AnalyticsPage />} />
                <Route path="/clubhouse" element={<ClubhousePage />} />
                <Route path="/xano-test" element={<XanoTest />} />
              </Routes>
            </Box>
          </Box>
        </Router>
      </CourseConfigProvider>
    </AuthProvider>
  );
};

export default App; 